import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { MultiSelect } from "@/components/ui/multi-select";
import { Plus, Trash2, RotateCcw } from "lucide-react";
import { ICustomField, IInvoiceItem } from '@/types';
import { Checkbox } from "@/components/ui/checkbox";


export enum InvoiceStatus {
  DRAFT = "draft",
  SENT = "sent",
  PAID = "paid",
  OVERDUE = "overdue"
}

export enum PaymentMethod {
  CASH = "cash",
  CHECK = "check",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  OTHER = "other"
}

const INVOICE_STATUS_OPTIONS = [
  { label: "Draft", value: InvoiceStatus.DRAFT },
  { label: "Sent", value: InvoiceStatus.SENT },
  { label: "Paid", value: InvoiceStatus.PAID },
  { label: "Overdue", value: InvoiceStatus.OVERDUE }
];

const PAYMENT_METHOD_OPTIONS = [
  { label: "Cash", value: PaymentMethod.CASH },
  { label: "Check", value: PaymentMethod.CHECK },
  { label: "Credit Card", value: PaymentMethod.CREDIT_CARD },
  { label: "Bank Transfer", value: PaymentMethod.BANK_TRANSFER },
  { label: "Other", value: PaymentMethod.OTHER }
];

const FLAG_OPTIONS = [
  { label: "Urgent", value: "urgent" },
  { label: "Recurring", value: "recurring" },
  { label: "Created by Provider", value: "createdByProvider" },
  { label: "Paid", value: "paid" },
  { label: "Unpaid", value: "unpaid" },
  { label: "Overdue", value: "overdue" },
  { label: "Draft", value: "draft" }
];

interface ManualInvoiceDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  manualInvoice: any;
  manualItems: any[];
  setManualInvoice: (invoice: any) => void;
  customers: any[];
  onCustomerChange: (customerId: string) => void;
  customFields: ICustomField[];
  setCustomFields: React.Dispatch<React.SetStateAction<ICustomField[]>>;
  onCreateInvoice: (items: IInvoiceItem[]) => void;
  getDisplaySubtotal: () => number;
  getDisplayTax: () => number;
  getDisplayTotal: () => number;
  handleSubtotalOverride: (value: string) => void;
  handleTaxOverride: (value: string) => void;
  handleTotalOverride: (value: string) => void;
  resetToCalculated: () => void;
}

export function ManualInvoiceDialog({
  isOpen,
  onOpenChange,
  manualInvoice,
  manualItems,
  setManualInvoice,
  customers,
  onCustomerChange,
  customFields,
  setCustomFields,
  onCreateInvoice,
  getDisplaySubtotal,
  getDisplayTax,
  getDisplayTotal,
  handleSubtotalOverride,
  handleTaxOverride,
  handleTotalOverride,
  resetToCalculated,
}: ManualInvoiceDialogProps) {
  const [items, setItems] = useState<IInvoiceItem[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [manualOverrides, _setManualOverrides] = useState({
    subTotal: false,
    tax: false,
    total: false
  });

  useEffect(() => {
    const convertedItems: IInvoiceItem[] = manualItems.map((item, index) => ({
      id: -(index + 1), // Negative ID for new items
      invoiceId: 0, // Will be set after invoice creation
      description: item.description || '',
      sku: '',
      quantity: item.quantity || 1,
      unit: '',
      unitPrice: item.unitPrice || 0,
      totalPrice: item.totalPrice || 0,
      discountAmount: item.discountAmount || 0,
      taxRate: item.taxRate || 0,
      taxAmount: item.taxAmount || 0,
      CreatedAt: new Date().toISOString(),
      UpdatedAt: new Date().toISOString()
    }));
    setItems(convertedItems);
  }, [manualItems]);

  // Helper functions from EditInvoiceDialog
  const handleFieldChange = (fieldName: string, value: string | number | boolean | string[]) => {
    setManualInvoice({ ...manualInvoice, [fieldName]: value });
  };

  const floatToPercentage = (value: number): number => {
    return Math.round((value || 0) * 100);
  };

  const percentageToFloat = (percentage: number): number => {
    return (percentage || 0) / 100;
  };

  const calculateItemTaxAmount = (item: IInvoiceItem): number => {
    const baseAmount = (item.unitPrice || 0) * (item.quantity || 0) - (item.discountAmount || 0);
    return baseAmount * (item.taxRate || 0);
  };

  const calculateItemTotalPrice = (item: IInvoiceItem): number => {
    const baseAmount = (item.unitPrice || 0) * (item.quantity || 0);
    const taxAmount = calculateItemTaxAmount(item);
    return baseAmount - (item.discountAmount || 0) + taxAmount;
  };

  const updateItemWithCalculations = (index: number, updatedItem: IInvoiceItem): IInvoiceItem[] => {
    const newItems = [...items];

    const itemWithCalculations = {
      ...updatedItem,
      taxAmount: calculateItemTaxAmount(updatedItem),
      totalPrice: calculateItemTotalPrice(updatedItem)
    };

    newItems[index] = itemWithCalculations;
    return newItems;
  };

  const updateItemField = (index: number, field: keyof IInvoiceItem, value: any): IInvoiceItem[] => {
    const newItems = [...items];
    const updatedItem = { ...newItems[index], [field]: value };
    newItems[index] = updatedItem;
    return newItems;
  };

  const handleCreateInvoice = () => {
    setIsSaving(true);
    onCreateInvoice(items);
  };

  const handleDateChange = (dateValue: string) => {
    if (!dateValue) {
      setManualInvoice({ ...manualInvoice, dueDate: "" });
      return;
    }
    const date = new Date(dateValue + 'T12:00:00.000Z');
    const isoString = date.toISOString();

    setManualInvoice({ ...manualInvoice, dueDate: isoString });
  };

  const getDateInputValue = () => {
    if (!manualInvoice.dueDate) return "";

    try {
      const date = new Date(manualInvoice.dueDate);

      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error("Error parsing date:", error);
      return "";
    }
  };

  const getCustomFieldValue = (label: string) => {
    const field = customFields.find(field => field.label === label);
    return field?.value || '';
  };

  const handleCustomFieldChange = (label: string, value: any) => {
    setCustomFields((prev: ICustomField[]) => {
      return prev.map(field =>
        field.label === label
          ? { ...field, value }
          : field
      );
    });
  };

  const renderCustomField = (field: ICustomField) => {
    const fieldId = `${'business'}_${field.label.replace(/\s+/g, '_').toLowerCase()}`;
    const value = getCustomFieldValue(field.label);

    switch (field.type) {
      case 'text':
        return (
          <Input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value,)}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );

      case 'number':
        return (
          <Input
            id={fieldId}
            type="number"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value)}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );

      case 'date':
        return (
          <Input
            id={fieldId}
            type="date"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value)}
            required={field.required}
          />
        );

      case 'select':
        return (
          <Select
            value={value}
            onValueChange={(selectedValue) => handleCustomFieldChange(field.label, selectedValue)}
          >
            <SelectTrigger id={fieldId}>
              <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'textarea':
        return (
          <Textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value)}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input
              id={fieldId}
              type="checkbox"
              checked={value === true || value === 'true'}
              onChange={(e) => handleCustomFieldChange(field.label, e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor={fieldId} className="text-sm font-normal">
              {field.placeholder || field.label}
            </Label>
          </div>
        );

      default:
        return null;
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Manual Invoice</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <Accordion type="multiple" defaultValue={["General", "DatesTerms", "Customer", "Items", "Financial", "Payment", "Provider", "Attachments"]} className="w-full">

            {/* 1. Customer/Recipient Information */}
            <AccordionItem value="Customer">
              <AccordionTrigger className="text-lg font-semibold">
                Customer/Recipient Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="customer" className="text-right">
                      Customer
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={manualInvoice.customerId}
                        onValueChange={onCustomerChange}
                      >
                        <SelectTrigger id="customer">
                          <SelectValue placeholder="Select customer" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id.toString()}>
                              {customer.name} - {customer.User?.email}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="name"
                        type="text"
                        value={manualInvoice.name || ''}
                        onChange={(e) => handleFieldChange('name', e.target.value)}
                        placeholder="Enter Name"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right">
                      Email
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="email"
                        type="email"
                        value={manualInvoice.email || ''}
                        onChange={(e) => handleFieldChange('email', e.target.value)}
                        placeholder="Enter email"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right">
                      Phone
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="phone"
                        type="tel"
                        value={manualInvoice.phone || ''}
                        onChange={(e) => handleFieldChange('phone', e.target.value)}
                        placeholder="Enter phone"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="address" className="text-right">
                      Address
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="address"
                        type="text"
                        value={manualInvoice.address || ''}
                        onChange={(e) => handleFieldChange('address', e.target.value)}
                        placeholder="Enter address"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="city" className="text-right">
                      City
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="city"
                        type="text"
                        value={manualInvoice.city || ''}
                        onChange={(e) => handleFieldChange('city', e.target.value)}
                        placeholder="Enter city"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="province" className="text-right">
                      Province/State
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="province"
                        type="text"
                        value={manualInvoice.province || ''}
                        onChange={(e) => handleFieldChange('province', e.target.value)}
                        placeholder="Enter province/state"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="country" className="text-right">
                      Country
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="country"
                        type="text"
                        value={manualInvoice.country || ''}
                        onChange={(e) => handleFieldChange('country', e.target.value)}
                        placeholder="Enter country"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 2. General Invoice Information */}
            <AccordionItem value="General">
              <AccordionTrigger className="text-lg font-semibold">
                General Invoice Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="status" className="text-right">
                      Status <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={manualInvoice.status || InvoiceStatus.DRAFT}
                        onValueChange={(value) => handleFieldChange('status', value)}
                      >
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          {INVOICE_STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="flags" className="text-right">
                      Flags
                    </Label>
                    <div className="col-span-3">
                      <MultiSelect
                        options={FLAG_OPTIONS}
                        selected={Array.isArray(manualInvoice.flags) ? manualInvoice.flags : []}
                        onChange={(flags) => handleFieldChange('flags', flags)}
                        placeholder="Select flags..."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="notes" className="text-right">
                      Notes
                    </Label>
                    <div className="col-span-3">
                      <Textarea
                        id="notes"
                        value={manualInvoice.notes || ''}
                        onChange={(e) => handleFieldChange('notes', e.target.value)}
                        placeholder="Enter notes"
                        rows={3}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="reference" className="text-right">
                      Reference
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="reference"
                        type="text"
                        value={manualInvoice.reference || ''}
                        onChange={(e) => handleFieldChange('reference', e.target.value)}
                        placeholder="Enter reference"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 3. Dates and Terms */}
            <AccordionItem value="DatesTerms">
              <AccordionTrigger className="text-lg font-semibold">
                Dates and Terms
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="dueDate" className="text-right">
                      Due Date <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="dueDate"
                        type="date"
                        value={getDateInputValue()}
                        onChange={(e) => handleDateChange(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paidOn" className="text-right">
                      Paid Date
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="paidOn"
                        type="date"
                        value={manualInvoice.paidOn || ''}
                        onChange={(e) => handleFieldChange('paidOn', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paymentTerms" className="text-right">
                      Payment Terms
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="paymentTerms"
                        type="text"
                        value={manualInvoice.paymentTerms || ''}
                        onChange={(e) => handleFieldChange('paymentTerms', e.target.value)}
                        placeholder="Enter payment terms"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 4. Invoice Items */}
            <AccordionItem value="Items">
              <AccordionTrigger className="text-lg font-semibold">
                Invoice Items
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4 py-4">
                  {items.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Item {index + 1}</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newItems = items.filter((_, i) => i !== index);
                            setItems(newItems);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Description</Label>
                          <Input
                            value={item.description || ''}
                            onChange={(e) => {
                              const newItems = updateItemField(index, 'description', e.target.value);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>SKU</Label>
                          <Input
                            value={item.sku || ''}
                            onChange={(e) => {
                              const newItems = updateItemField(index, 'sku', e.target.value);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Quantity</Label>
                          <Input
                            type="number"
                            value={item.quantity || 0}
                            onChange={(e) => {
                              const updatedItem = { ...item, quantity: parseFloat(e.target.value) || 0 };
                              const newItems = updateItemWithCalculations(index, updatedItem);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Unit</Label>
                          <Input
                            value={item.unit || ''}
                            onChange={(e) => {
                              const newItems = updateItemField(index, 'unit', e.target.value);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Unit Price</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={item.unitPrice || 0}
                            onChange={(e) => {
                              const updatedItem = { ...item, unitPrice: parseFloat(e.target.value) || 0 };
                              const newItems = updateItemWithCalculations(index, updatedItem);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Discount Amount</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={item.discountAmount || 0}
                            onChange={(e) => {
                              const updatedItem = { ...item, discountAmount: parseFloat(e.target.value) || 0 };
                              const newItems = updateItemWithCalculations(index, updatedItem);
                              setItems(newItems);
                            }}
                          />
                        </div>
                        <div>
                          <Label>Tax Rate (%)</Label>
                          <div className="relative">
                            <Input
                              type="number"
                              step="1"
                              min="0"
                              max="100"
                              value={floatToPercentage(item.taxRate)}
                              onChange={(e) => {
                                const percentageValue = parseInt(e.target.value) || 0;
                                const updatedItem = { ...item, taxRate: percentageToFloat(percentageValue) };
                                const newItems = updateItemWithCalculations(index, updatedItem);
                                setItems(newItems);
                              }}
                              placeholder="0"
                              className="pr-8"
                            />
                            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                              %
                            </span>
                          </div>
                        </div>
                        <div>
                          <Label>Tax Amount</Label>
                          <div className="relative">
                            <Input
                              type="number"
                              step="0.01"
                              value={item.taxAmount || 0}
                              onChange={(e) => {
                                const newItems = updateItemField(index, 'taxAmount', parseFloat(e.target.value) || 0);
                                setItems(newItems);
                              }}
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                              Auto
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Calculated automatically from tax rate
                          </p>
                        </div>
                        <div className="col-span-2">
                          <Label>Total Price</Label>
                          <div className="relative">
                            <Input
                              type="number"
                              step="0.01"
                              value={item.totalPrice || 0}
                              onChange={(e) => {
                                const newItems = updateItemField(index, 'totalPrice', parseFloat(e.target.value) || 0);
                                setItems(newItems);
                              }}
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">
                              Auto
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Calculated automatically from price, quantity, discount, and tax
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newItem = {
                        id: -(Date.now()), // Negative ID for new items
                        invoiceId: 0,
                        description: '',
                        sku: '',
                        quantity: 1,
                        unit: '',
                        unitPrice: 0,
                        discountAmount: 0,
                        taxRate: 0,
                        taxAmount: 0,
                        totalPrice: 0,
                        CreatedAt: new Date().toISOString(),
                        UpdatedAt: new Date().toISOString()
                      } as IInvoiceItem;

                      const newItems = [...items, newItem];
                      setItems(newItems);
                    }}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 5. Financial Summary */}
            <AccordionItem value="Financial">
              <AccordionTrigger className="text-lg font-semibold">
                Financial Summary
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="subTotal" className="text-right font-medium">
                      Subtotal
                      {manualOverrides.subTotal && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => resetToCalculated()}
                          className="ml-2 h-6 w-6 p-0"
                          title="Reset to calculated value"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="subTotal"
                        type="number"
                        step="0.01"
                        value={getDisplaySubtotal().toFixed(2)}
                        onChange={(e) => handleSubtotalOverride(e.target.value)}
                        placeholder="0.00"
                        className={manualInvoice.isSubtotalOverridden ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""}
                      />
                      {manualInvoice.isSubtotalOverridden ? (
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Manual override active
                        </p>
                      ) : (
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                          Automatically calculated from items
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tax" className="text-right font-medium">
                      Tax Amount
                      {manualOverrides.tax && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => resetToCalculated()}
                          className="ml-2 h-6 w-6 p-0"
                          title="Reset to calculated value"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="tax"
                        type="number"
                        step="0.01"
                        value={getDisplayTax().toFixed(2)}
                        onChange={(e) => handleTaxOverride(e.target.value)}
                        placeholder="0.00"
                        className={manualInvoice.isTaxOverridden ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""}
                      />
                      {manualInvoice.isTaxOverridden ? (
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Manual override active
                        </p>
                      ) : (
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                          Automatically calculated from items
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="total" className="text-right font-medium">
                      Total
                      {manualOverrides.total && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => resetToCalculated()}
                          className="ml-2 h-6 w-6 p-0"
                          title="Reset to calculated value"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="total"
                        type="number"
                        step="0.01"
                        value={getDisplayTotal().toFixed(2)}
                        onChange={(e) => handleTotalOverride(e.target.value)}
                        placeholder="0.00"
                        className={manualInvoice.isTotalOverridden ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""}
                      />
                      {manualInvoice.isTotalOverridden ? (
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Manual override active
                        </p>
                      ) : (
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                          Automatically calculated (subtotal + tax)
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="taxRate" className="text-right">
                      Tax Rate (%)
                    </Label>
                    <div className="col-span-3">
                      <div className="relative">
                        <Input
                          id="taxRate"
                          type="number"
                          step="1"
                          min="0"
                          max="100"
                          value={floatToPercentage(manualInvoice.taxRate)}
                          onChange={(e) => {
                            const percentageValue = parseInt(e.target.value) || 0;
                            handleFieldChange('taxRate', percentageToFloat(percentageValue));
                          }}
                          placeholder="0"
                          className="pr-8"
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                          %
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Enter as percentage (e.g., 15 for 15%)
                      </p>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 6. Payment Information */}
            <AccordionItem value="Payment">
              <AccordionTrigger className="text-lg font-semibold">
                Payment Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="Paid" className="text-right">
                      Paid
                    </Label>
                    <div className="col-span-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="Paid"
                          checked={manualInvoice.paid === true}
                          onCheckedChange={(checked) => handleFieldChange('Paid', checked)}
                        />
                        <Label htmlFor="Paid" className="text-sm font-normal">
                          Mark as paid
                        </Label>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paymentMethod" className="text-right">
                      Payment Method
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={manualInvoice.paymentMethod || ''}
                        onValueChange={(value) => handleFieldChange('paymentMethod', value)}
                      >
                        <SelectTrigger id="paymentMethod">
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent>
                          {PAYMENT_METHOD_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="paidBy" className="text-right">
                      Paid By
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="paidBy"
                        type="text"
                        value={manualInvoice.paidBy || ''}
                        onChange={(e) => handleFieldChange('paidBy', e.target.value)}
                        placeholder="Enter who paid"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="accountName" className="text-right">
                      Account Name
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="accountName"
                        type="text"
                        value={manualInvoice.accountName || ''}
                        onChange={(e) => handleFieldChange('accountName', e.target.value)}
                        placeholder="Enter account name"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="accountNumber" className="text-right">
                      Account Number
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="accountNumber"
                        type="text"
                        value={manualInvoice.accountNumber || ''}
                        onChange={(e) => handleFieldChange('accountNumber', e.target.value)}
                        placeholder="Enter account number"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="sortCode" className="text-right">
                      Sort Code
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="sortCode"
                        type="text"
                        value={manualInvoice.sortCode || ''}
                        onChange={(e) => handleFieldChange('sortCode', e.target.value)}
                        placeholder="Enter sort code"
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 7. Provider Information */}
            <AccordionItem value="Provider">
              <AccordionTrigger className="text-lg font-semibold">
                Provider Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="providerId" className="text-right">
                      Provider ID
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="providerId"
                        type="number"
                        value={manualInvoice.providerId || 0}
                        readOnly
                        className='bg-muted text-muted-foreground'
                      />
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 8. Attachments and Custom Data */}
            <AccordionItem value="Attachments">
              <AccordionTrigger className="text-lg font-semibold">
                Attachments and Custom Data
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4 py-4">
                  {/* Custom Fields */}
                  <div className="border-t pt-4 mt-4">
                    <h4 className="text-sm font-medium mb-3">Custom Fields</h4>
                    {customFields.length > 0 ? (
                      customFields.map((field) => (
                        <div key={field.label} className="grid grid-cols-4 items-center gap-4 mb-4">
                          <Label htmlFor={field.label} className="text-right">
                            {field.label}
                          </Label>
                          <div className="col-span-3">
                            {renderCustomField(field)}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-sm text-muted-foreground p-4 border rounded-lg bg-muted/20">
                        <p>No custom fields configured for this customer.</p>
                        <p className="mt-1">Custom fields will appear here when configured.</p>
                      </div>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

          </Accordion>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleCreateInvoice} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              'Create Invoice'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
