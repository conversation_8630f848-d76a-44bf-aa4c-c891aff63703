import { useContext, useState, useEffect } from 'react';
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { getFromApi, postToApi, putToApi, deleteTo<PERSON>pi } from "@/lib/api";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus, FileText, Receipt, Download, Eye, Printer, CreditCard, FileUp } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { ManualInvoiceDialog, BatchManagementTab } from "@/components/invoices";
import { WorkOrderInvoiceDialog } from '@/components/invoices';
import { ICustomField, IInvoiceRes, ISessionUser, IInvoiceItem, IProviderRes, ICustomerRes } from '@/types';
import { EditInvoiceDialog } from '@/components/invoices/';
import { currencyValue } from "@/lib/utils";

type InvoiceTabType = "all" | "draft" | "sent" | "paid" | "overdue" | "batch";

type IInvoiceCreate = Partial<
  Omit<
    IInvoiceRes,
    | "id"
    | "CreatedAt"
    | "UpdatedAt"
    | "Num"
    | "business"
    | "currency"
    | "provider"
    | "items"
    | "data"
    | "workOrder"
  >
> & {
  businessId: number;
  locationId: number;
  providerId: number;
  status: string;
  value: number;
};

const MOCK_BUSINESS_CUSTOM_FIELDS_1: ICustomField[] = [
  { type: "text", value: "", label: "Project Code", placeholder: "Enter project code", options: [], required: true },
  {
    type: "select", value: "", label: "Department", options: [
      { label: "Sales", value: "sales" },
      { label: "Marketing", value: "marketing" },
      { label: "Operations", value: "operations" },
      { label: "Finance", value: "finance" }
    ], required: false
  },
  { type: "number", value: "", label: "Priority Level", placeholder: "1-10", options: [], required: false },
  { type: "date", value: "", label: "Project Deadline", options: [], required: false },
  { type: "textarea", value: "", label: "Special Instructions", placeholder: "Enter any special instructions", options: [], required: false },
  { type: "checkbox", value: false, label: "Rush Order", options: [], required: false }
];

const MOCK_BUSINESS_CUSTOM_FIELDS_2: ICustomField[] = [
  {
    type: "select", value: "", label: "Priority", options: [
      { label: "High", value: "high" },
      { label: "Medium", value: "medium" },
      { label: "Low", value: "low" }
    ], required: false
  },
  { type: "text", value: "", label: "PO Number", placeholder: "Enter PO number", options: [], required: true },
  { type: "date", value: "", label: "Expected Delivery", options: [], required: true },
  { type: "checkbox", value: false, label: "Requires Signature", options: [], required: false }
];

const MOCK_BUSINESS_CUSTOM_FIELDS_3: ICustomField[] = [
  { type: "text", value: "", label: "Contract ID", placeholder: "Enter contract ID", options: [], required: true },
  {
    type: "select", value: "", label: "Service Type", options: [
      { label: "Installation", value: "installation" },
      { label: "Maintenance", value: "maintenance" },
      { label: "Repair", value: "repair" },
      { label: "Consultation", value: "consultation" }
    ], required: true
  },
  { type: "textarea", value: "", label: "Service Details", placeholder: "Describe service details", options: [], required: false },
  { type: "number", value: "", label: "Service Hours", placeholder: "Enter hours", options: [], required: false },
  { type: "date", value: "", label: "Service Date", options: [], required: true }
];

const randomMockData = () => {
  const random = Math.floor(Math.random() * 3);
  if (random === 0) {
    return MOCK_BUSINESS_CUSTOM_FIELDS_1;
  } else if (random === 1) {
    return MOCK_BUSINESS_CUSTOM_FIELDS_2;
  } else {
    return MOCK_BUSINESS_CUSTOM_FIELDS_3;
  }
}

export function InvoicesPage() {
  const { user }: { user: ISessionUser } = useContext(ConfigContext);
  const {
    provider,
    setupUser,
    businesses,
  }: { provider: IProviderRes, setupUser: any, businesses: ICustomerRes[] } = useContext(BusinessContext);
  const [invoices, setInvoices] = useState<IInvoiceRes[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [activeTab, setActiveTab] = useState<InvoiceTabType>("all");
  const [isCreateInvoiceDialogOpen, setIsCreateInvoiceDialogOpen] = useState(false);
  const [isManualInvoiceDialogOpen, setIsManualInvoiceDialogOpen] = useState(false);
  const [completedWorkOrders, setCompletedWorkOrders] = useState([]);
  const [customFields, setCustomFields] = useState<ICustomField[]>([]);
  const [manualItems, setManualItems] = useState<Partial<IInvoiceItem>[]>([]);
  const [manualInvoice, setManualInvoice] = useState<IInvoiceCreate>({
    businessId: 0,
    locationId: 0,
    providerId: 0,
    status: "draft",
    value: 0,
  });
  const [manualValues, setManualValues] = useState({
    subTotal: null,
    tax: null,
    total: null
  });
  const [isInvoiceDataOverridden, setInvoiceDataOverride] = useState({
    subTotal: false,
    tax: false,
    total: false
  });
  const { toast } = useToast();

  useEffect(() => {
    setupUser(user);
    setCustomFields(randomMockData());

    if (provider) {
      fetchInvoices()
    }
  }, [provider, user]);

  function fetchInvoices() {
    let url = `/api/v1/provider/${provider.id}/invoices/90`;
    getFromApi(url, user, (response) => {
      console.log("InvoicesPage useEffect", response.result);
      let invoices = response.result || [];
      setInvoices(invoices.sort((a, b) => a.id - b.id));
    },
      (error) => {
        console.error("Error fetching invoices:", error);
      });
  }

  const handleCustomerChange = (customerId) => {
    const selectedCustomer = businesses.find(c => c.id.toString() === customerId);
    if (selectedCustomer) {
      const primaryLocation = selectedCustomer.locations && selectedCustomer.locations[0];
      const customerUser = selectedCustomer.User;
      const customFields = selectedCustomer.fields;

      setCustomFields(randomMockData);
      setManualInvoice(prev => ({
        ...prev,
        customerId,
        businessId: selectedCustomer.id,
        name: selectedCustomer.name,
        locationId: primaryLocation?.id || 0,
        country: primaryLocation?.country || "",
        province: primaryLocation?.province || "",
        city: primaryLocation?.city || "",
        address: primaryLocation?.address || "",
        email: customerUser?.email || "",
        phone: customerUser?.phone || ""
      }));
    } else {
      setManualInvoice(prev => ({
        ...prev,
        customerId,
        locationId: 0,
        country: "",
        province: "",
        city: "",
        address: "",
        email: "",
        phone: ""
      }));
    }
  };

  const handleOpenCreateInvoiceDialog = () => {
    if (provider) {
      let url = `/api/v1/provider/${provider.id}/work_orders/90`;
      getFromApi(url, user, (response) => {
        const workOrders = response.result || [];
        const completed = workOrders.filter(wo => wo.status === 'completed' && !wo.invoice);
        console.log(completed)
        setCompletedWorkOrders(completed);
        setIsCreateInvoiceDialogOpen(true);
      },
        (error) => {
          console.error("Error fetching work orders:", error);
          toast({
            title: "Error",
            description: "Could not fetch completed work orders",
            variant: "destructive",
          });
        });
    }
  };

  const processManualInvoiceItems = (invoiceId: number, items: IInvoiceItem[]) => {
    const errors: string[] = [];
    console.log("Processing manual invoice items", invoiceId, items);

    for (const item of items) {
      const itemData = {
        invoiceId: invoiceId,
        description: item.description,
        sku: item.sku,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        discountAmount: item.discountAmount,
        taxRate: item.taxRate,
        taxAmount: item.taxAmount
      };
      const createUrl = `/api/v1/invoice/${invoiceId}/items`;
      postToApi(createUrl, user, itemData,
        (response: any) => console.log(`Res create ${item.description}`, response),
        (error: any) => {
          errors.push(`Create ${item.description}: ${error.message}`);
          console.log(`Err create ${item.description}`, error)
        }
      );
    }

    if (errors.length > 0) {
      toast({
        title: "Item Creation Errors",
        description: `Some items could not be created: ${errors.join(', ')}`,
        variant: "destructive",
      });
    }
  }

  const createManualInvoice = (items: IInvoiceItem[]) => {
    if (!provider || !user) return;

    console.log("manualInvoice", manualInvoice);

    let subtotal = isInvoiceDataOverridden.subTotal ? manualValues.subTotal : calculateSubtotal();
    let tax = isInvoiceDataOverridden.tax ? manualValues.tax : calculateTax();
    let total = isInvoiceDataOverridden.total ? manualValues.total : calculateTotal();

    if (!manualItems || manualItems.length === 0) {
      if (!isInvoiceDataOverridden.total && manualValues.total) {
        total = manualValues.total;
      }
    }

    const invoiceData = {
      ...manualInvoice,
      providerId: provider.id,
      subtotal: subtotal * 100,
      tax: tax * 100,
      total: total * 100,
    };

    const url = `/api/v1/invoice`;
    console.log("invoiceData:", invoiceData)

    postToApi(url, user, invoiceData,
      (response) => {
        console.log("response:", response.result);

        if (items && items.length > 0) {
          processManualInvoiceItems(response.result.id, items);
        }

        fetchInvoices()
        setManualInvoice({
          businessId: 0,
          locationId: 0,
          providerId: 0,
          name: "",
          email: "",
          country: "",
          province: "",
          city: "",
          address: "",
          phone: "",
          notes: "",
          terms: "",
          tax: 0,
          taxRate: 0,
          taxNumber: "",
          taxType: "",
          subTotal: 0,
          value: 0,
          total: 0,
          status: "draft",
          paymentMethod: "",
          flags: [],
          dueOn: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        });

        setManualItems([]);
        setIsManualInvoiceDialogOpen(false);

        toast({
          title: "Invoice Created",
          description: `Invoice #${response.result.number} created successfully`,
        });
      },
      (error) => {
        console.error("Error creating manual invoice:", error);
        toast({
          title: "Invoice Creation Failed",
          description: "Could not create invoice",
          variant: "destructive",
        });
      }
    );
  };

  const calculateSubtotal = () => {
    if (!manualItems || manualItems.length === 0) return 0;

    return manualItems.reduce((sum, item) => {
      const quantity = (item.quantity) || 0;
      const unitPrice = (item.unitPrice) || 0;
      return sum + (quantity * unitPrice);
    }, 0);
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal();
    const taxRate = (manualInvoice.taxRate) || 0;
    return subtotal * (taxRate / 100);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax();
    return subtotal + tax;
  };

  const getDisplaySubtotal = () => {
    return isInvoiceDataOverridden.subTotal ? manualValues.subTotal : calculateSubtotal();
  };

  const getDisplayTax = () => {
    return isInvoiceDataOverridden.tax ? manualValues.tax : calculateTax();
  };

  const getDisplayTotal = () => {
    return isInvoiceDataOverridden.total ? manualValues.total : calculateTotal();
  };

  const handleSubtotalOverride = (value) => {
    const numValue = parseFloat(value) || 0;
    setManualValues({
      ...manualValues,
      subTotal: numValue
    });
    setInvoiceDataOverride({
      ...isInvoiceDataOverridden,
      subTotal: true
    });
  };

  const handleTaxOverride = (value) => {
    const numValue = parseFloat(value) || 0;
    setManualValues({
      ...manualValues,
      tax: numValue,
    });
    setInvoiceDataOverride({
      ...isInvoiceDataOverridden,
      tax: true
    });
  };

  const handleTotalOverride = (value) => {
    const numValue = parseFloat(value) || 0;
    setManualValues({
      ...manualValues,
      total: numValue
    });
    setInvoiceDataOverride({
      ...isInvoiceDataOverridden,
      total: true
    });
  };

  const resetToCalculated = () => {
    setManualValues({
      subTotal: null,
      tax: null,
      total: null
    });
    setInvoiceDataOverride({
      subTotal: false,
      tax: false,
      total: false
    });
  };

  const filterInvoices = (status) => {
    if (status === "all") return invoices;
    return invoices.filter(invoice => invoice.status === status);
  };

  return selectedInvoice ? (
    <InvoiceDetails
      invoice={selectedInvoice}
      setInvoice={setSelectedInvoice}
      fetchInvoices={fetchInvoices}
      onBack={() => setSelectedInvoice(null)}
      user={user}
    />
  ) : (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Invoices</h1>
          <p className="text-muted-foreground">View and manage customer invoices.</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setIsManualInvoiceDialogOpen(true)}
          >
            <FileUp className="h-4 w-4" />
            Manual Invoice
          </Button>
          <Button
            className="flex items-center gap-2"
            onClick={handleOpenCreateInvoiceDialog}
          >
            <Plus className="h-4 w-4" />
            From Work Order
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" onValueChange={(value: InvoiceTabType) => setActiveTab(value)}>
        <TabsList>
          <TabsTrigger value="all">All Invoices</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
          <TabsTrigger value="paid">Paid</TabsTrigger>
          <TabsTrigger value="overdue">Overdue</TabsTrigger>
          <TabsTrigger value="batch">Batch</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {invoices && invoices.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterInvoices("all").map((invoice) => (
                    <InvoiceItem
                      key={invoice.id}
                      invoice={invoice}
                      onSelect={() => setSelectedInvoice(invoice)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Invoices Found</h3>
              <p className="text-muted-foreground mb-4">
                Create your first invoice or generate one from a completed work order.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="draft" className="space-y-4">
          {filterInvoices("draft").length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterInvoices("draft").map((invoice) => (
                    <InvoiceItem
                      key={invoice.id}
                      invoice={invoice}
                      onSelect={() => setSelectedInvoice(invoice)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Draft Invoices</h3>
              <p className="text-muted-foreground mb-4">
                Draft invoices will appear here.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="sent" className="space-y-4">
          {filterInvoices("sent").length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterInvoices("sent").map((invoice) => (
                    <InvoiceItem
                      key={invoice.id}
                      invoice={invoice}
                      onSelect={() => setSelectedInvoice(invoice)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Sent Invoices</h3>
              <p className="text-muted-foreground mb-4">
                Sent invoices will appear here.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="paid" className="space-y-4">
          {filterInvoices("paid").length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterInvoices("paid").map((invoice) => (
                    <InvoiceItem
                      key={invoice.id}
                      invoice={invoice}
                      onSelect={() => setSelectedInvoice(invoice)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Paid Invoices</h3>
              <p className="text-muted-foreground mb-4">
                Paid invoices will appear here.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="overdue" className="space-y-4">
          {filterInvoices("overdue").length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice #</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Work Order</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterInvoices("overdue").map((invoice) => (
                    <InvoiceItem
                      key={invoice.id}
                      invoice={invoice}
                      onSelect={() => setSelectedInvoice(invoice)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Overdue Invoices</h3>
              <p className="text-muted-foreground mb-4">
                Overdue invoices will appear here.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="batch" className="space-y-4">
          <BatchManagementTab
            user={user}
            provider={provider}
            invoices={invoices}
            customers={businesses}
            fetchInvoices={fetchInvoices}
          />
        </TabsContent>
      </Tabs>

      {/* Create Invoice from Work Order Dialog */}
      <WorkOrderInvoiceDialog
        isOpen={isCreateInvoiceDialogOpen}
        setIsOpen={setIsCreateInvoiceDialogOpen}
        user={user}
        provider={provider}
        workOrders={completedWorkOrders}
        setWorkOrders={setCompletedWorkOrders}
        setInvoices={setInvoices}
      />

      {/* Create Manual Invoice Dialog */}
      <ManualInvoiceDialog
        isOpen={isManualInvoiceDialogOpen}
        onOpenChange={setIsManualInvoiceDialogOpen}
        manualInvoice={manualInvoice}
        manualItems={manualItems}
        setManualInvoice={setManualInvoice}
        customers={businesses}
        onCustomerChange={handleCustomerChange}
        customFields={customFields}
        setCustomFields={setCustomFields}
        onCreateInvoice={createManualInvoice}
        getDisplaySubtotal={getDisplaySubtotal}
        getDisplayTax={getDisplayTax}
        getDisplayTotal={getDisplayTotal}
        handleSubtotalOverride={handleSubtotalOverride}
        handleTaxOverride={handleTaxOverride}
        handleTotalOverride={handleTotalOverride}
        resetToCalculated={resetToCalculated}
      />
    </div>
  );
}

function InvoiceItem({ invoice, onSelect }: { invoice: IInvoiceRes, onSelect: () => void }) {
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'sent':
        return <Badge variant="secondary">Sent</Badge>;
      case 'paid':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Paid</Badge>;
      case 'overdue':
        return <Badge variant="destructive">Overdue</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <TableRow
      className="cursor-pointer hover:bg-muted/50"
      onClick={onSelect}
    >
      <TableCell className="font-medium">#{invoice.number || invoice.Num}</TableCell>
      <TableCell>{invoice.name || 'N/A'}</TableCell>
      <TableCell>{invoice.workOrder ? `#${invoice.workOrder.id}` : 'N/A'}</TableCell>
      <TableCell>{formatDate(invoice.CreatedAt)}</TableCell>
      <TableCell>{formatDate(invoice.dueOn)}</TableCell>
      <TableCell className="font-medium">{currencyValue(invoice.total)}</TableCell>
      <TableCell>{getStatusBadge(invoice.status)}</TableCell>
      <TableCell>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={(e) => {
            e.stopPropagation();
            window.open(`/api/v1/invoice/${invoice.id}/pdf`, '_blank');
          }}>
            <Download className="h-4 w-4" />
            <span className="sr-only">Download</span>
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Eye className="h-4 w-4" />
            <span className="sr-only">View</span>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}

interface InvoiceDetailsProps {
  invoice: IInvoiceRes;
  setInvoice: (invoice: IInvoiceRes) => void;
  fetchInvoices: () => void;
  onBack: () => void;
  user: ISessionUser;
}

function InvoiceDetails({ invoice, setInvoice, fetchInvoices, onBack, user }: InvoiceDetailsProps) {
  const [isEditInvoiceDialogOpen, setIsEditInvoiceDialogOpen] = useState(false);
  const { toast } = useToast();

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const updateInvoice = async (updatedInvoice: any, itemChanges: any) => {
    const url = `/api/v1/invoice/${updatedInvoice.id}`;
    const data = structuredClone(updatedInvoice);
    console.log("url", url)
    console.log("data", data)

    if (itemChanges && (itemChanges.created.length > 0 || itemChanges.updated.length > 0 || itemChanges.deleted.length > 0)) {
      processInvoiceItemChanges(updatedInvoice.id, itemChanges);
    }

    putToApi(url, user, data,
      (response) => {
        toast({
          title: "Invoice Updated",
          description: `Invoice updated successfully`,
        });
        console.log("put update response:", response.result)
        fetchInvoices();
        setInvoice(response.result);
      },
      (error) => {
        toast({
          title: "Update Failed",
          description: "Could not update invoice",
          variant: "destructive",
        });
        console.error("Error updating invoice:", error);
      })

    setIsEditInvoiceDialogOpen(false);
  }

  const processInvoiceItemChanges = async (invoiceId: number, itemChanges: {
    created: Omit<IInvoiceItem, 'id' | 'invoiceId' | 'CreatedAt' | 'UpdatedAt'>[];
    updated: IInvoiceItem[];
    deleted: (string | number)[];
  }) => {
    const errors: string[] = [];

    console.log("item changes", itemChanges)

    for (const itemId of itemChanges.deleted) {
      const deleteUrl = `/api/v1/invoice/${invoiceId}/items/${itemId}`;
      const deleteData = {
        id: itemId,
        invoiceId: invoiceId
      }
      console.log("delete url", deleteUrl)
      console.log("delete data", deleteData)
      deleteToApi(deleteUrl, user, deleteData,
        (response) => console.log(`Res delete ${itemId}`, response),
        (error) => {
          errors.push(`Delete ${itemId}: ${error.message}`);
          console.error(`Err delete ${itemId}`, error)
        }
      );
    }

    for (const item of itemChanges.created) {
      const itemData = {
        invoiceId: invoiceId,
        description: item.description,
        sku: item.sku,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        discountAmount: item.discountAmount,
        taxRate: item.taxRate,
        taxAmount: item.taxAmount
      };
      const createUrl = `/api/v1/invoice/${invoiceId}/items`;
      console.log("create url", createUrl)
      console.log("create data", itemData)
      postToApi(createUrl, user, itemData,
        (response: any) => console.log(`Res create ${item.description}`, response),
        (error: any) => {
          errors.push(`Create ${item.description}: ${error.message}`);
          console.log(`Err create ${item.description}`, error)
        }
      );
    }

    for (const item of itemChanges.updated) {
      const itemData = {
        id: item.id,
        invoiceId: invoiceId,
        description: item.description,
        sku: item.sku,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        discountAmount: item.discountAmount,
        taxRate: item.taxRate,
        taxAmount: item.taxAmount
      };
      const updateUrl = `/api/v1/invoice/${invoiceId}/items/${item.id}`;
      console.log("update url", updateUrl)
      console.log("update data", itemData)
      putToApi(updateUrl, user, itemData,
        (response: any) => console.log(`Res update ${item.id}`, response),
        (error: any) => {
          errors.push(`Update ${item.id}: ${error.message}`);
          console.log(`Err update ${item.id}`, error);
        }
      );
    }
  }

  return isEditInvoiceDialogOpen ? (
    <EditInvoiceDialog
      isOpen={isEditInvoiceDialogOpen}
      onClose={() => setIsEditInvoiceDialogOpen(false)}
      invoice={invoice}
      onSave={updateInvoice}
    />
  ) : (
    <div className="space-y-8">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={onBack}
          className="h-8 w-8"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="m15 18-6-6 6-6" /></svg>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Invoice #{invoice.number || invoice.id}</h1>
          <p className="text-muted-foreground">
            {invoice.business?.name} - {formatDate(invoice.CreatedAt)}
          </p>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" className="flex items-center gap-2">
          <Printer className="h-4 w-4" />
          Print
        </Button>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Download PDF
        </Button>
        <Button variant="outline" className="flex items-center gap-2" onClick={() => setIsEditInvoiceDialogOpen(true)}>
          <FileText className="h-4 w-4" />
          Edit
        </Button>
        <Button className="flex items-center gap-2">
          <CreditCard className="h-4 w-4" />
          Mark as Paid
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-medium mb-4">Invoice Details</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Invoice Number:</span>
                <span className="font-medium">#{invoice.number || invoice.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Date Issued:</span>
                <span>{formatDate(invoice.CreatedAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Due Date:</span>
                <span>{formatDate(invoice.dueOn)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Status:</span>
                <span>{invoice.status}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Work Order:</span>
                <span>{invoice.workOrder ? `#${invoice.workOrder.num}` : 'N/A'}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-medium mb-4">Customer Information</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Name:</span>
                <span>{invoice.name || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Address:</span>
                <span>{invoice.address || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Email:</span>
                <span>{invoice.email || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Phone:</span>
                <span>{invoice.phone || 'N/A'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium mb-4">Invoice Items</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Description</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Unit Price</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoice.items && invoice.items.length > 0 ? (
                invoice.items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.description}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>${item.unitPrice?.toFixed(2) || '0.00'}</TableCell>
                    <TableCell className="text-right">${(item.quantity * item.unitPrice).toFixed(2)}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-muted-foreground">No items found</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          <div className="mt-6 space-y-2 border-t pt-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal:</span>
              <span>${invoice.subTotal?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Tax ({invoice.taxRate || 0}%):</span>
              <span>${invoice.tax?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total:</span>
              <span>${invoice.total?.toFixed(2) || '0.00'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium mb-4">Payment Information</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Payment Method:</span>
              <span>{invoice.paymentMethod || 'Not specified'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Payment Status:</span>
              <span>{invoice.status}</span>
            </div>
            {invoice.paidOn && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Payment Date:</span>
                <span>{formatDate(invoice.paidOn)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium mb-4">Notes</h3>
          <p className="text-muted-foreground">
            {invoice.notes || 'No notes provided for this invoice.'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
